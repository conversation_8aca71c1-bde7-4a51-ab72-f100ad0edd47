import React, { useEffect } from 'react';
import {
  CpuChipIcon,
  GlobeAltIcon,
  PlayIcon,
  SignalIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  EyeIcon,
  EyeSlashIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  XMarkIcon,
  BellIcon
} from '@heroicons/react/24/outline';
import { Button } from './Button';
import { useMonitorStore } from '../../stores/monitorStore';

interface MonitorData {
  system: {
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    timestamp: string;
    uptime: string;
    userAgent: string;
  };
  network: {
    online: boolean;
    connectionType: string;
    effectiveType: string;
    downlink: number;
    rtt: number;
    saveData: boolean;
  };
  playback: {
    currentUrl: string | null;
    status: 'idle' | 'loading' | 'playing' | 'error';
    bufferHealth: number;
    quality: string;
    fps: number;
    resolution: string;
  };
  performance: {
    loadTime: number;
    renderTime: number;
    frameDrops: number;
    errorCount: number;
  };
}

interface MonitorSectionConfig {
  id: keyof MonitorData;
  title: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  color: string;
  enabled: boolean;
}

export const MonitorDashboard: React.FC = () => {
  const [monitorData, setMonitorData] = useState<MonitorData>({
    system: {
      memory: { used: 0, total: 0, percentage: 0 },
      timestamp: new Date().toISOString(),
      uptime: '0s',
      userAgent: navigator.userAgent
    },
    network: {
      online: navigator.onLine,
      connectionType: 'unknown',
      effectiveType: 'unknown',
      downlink: 0,
      rtt: 0,
      saveData: false
    },
    playback: {
      currentUrl: null,
      status: 'idle',
      bufferHealth: 0,
      quality: 'auto',
      fps: 0,
      resolution: 'unknown'
    },
    performance: {
      loadTime: 0,
      renderTime: 0,
      frameDrops: 0,
      errorCount: 0
    }
  });

  const [sections, setSections] = useState<MonitorSectionConfig[]>([
    { id: 'system', title: '系統監控', icon: CpuChipIcon, color: 'blue', enabled: true },
    { id: 'network', title: '網路狀態', icon: GlobeAltIcon, color: 'green', enabled: true },
    { id: 'playback', title: '播放狀態', icon: PlayIcon, color: 'purple', enabled: true },
    { id: 'performance', title: '效能監控', icon: ChartBarIcon, color: 'orange', enabled: false }
  ]);

  const [isVisible, setIsVisible] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(2000);

  // 獲取系統記憶體資訊
  const getMemoryInfo = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
        percentage: Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100)
      };
    }
    return { used: 0, total: 0, percentage: 0 };
  };

  // 獲取網路資訊
  const getNetworkInfo = () => {
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    
    if (connection) {
      return {
        online: navigator.onLine,
        connectionType: connection.type || 'unknown',
        effectiveType: connection.effectiveType || 'unknown',
        downlink: connection.downlink || 0,
        rtt: connection.rtt || 0,
        saveData: connection.saveData || false
      };
    }

    return {
      online: navigator.onLine,
      connectionType: 'unknown',
      effectiveType: 'unknown',
      downlink: 0,
      rtt: 0,
      saveData: false
    };
  };

  // 獲取效能資訊
  const getPerformanceInfo = () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    return {
      loadTime: navigation ? Math.round(navigation.loadEventEnd - navigation.navigationStart) : 0,
      renderTime: navigation ? Math.round(navigation.domContentLoadedEventEnd - navigation.navigationStart) : 0,
      frameDrops: 0, // 需要透過 requestAnimationFrame 追蹤
      errorCount: 0 // 需要透過 error listener 追蹤
    };
  };

  // 更新監控資料
  const updateMonitorData = () => {
    setMonitorData(prev => ({
      ...prev,
      system: {
        ...prev.system,
        memory: getMemoryInfo(),
        timestamp: new Date().toISOString(),
        uptime: `${Math.round((Date.now() - performance.timeOrigin) / 1000)}s`
      },
      network: getNetworkInfo(),
      performance: getPerformanceInfo()
    }));
  };

  // 自動刷新
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (autoRefresh && isVisible) {
      interval = setInterval(updateMonitorData, refreshInterval);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [autoRefresh, isVisible, refreshInterval]);

  // 初始化資料
  useEffect(() => {
    updateMonitorData();
  }, []);

  // 切換區塊顯示狀態
  const toggleSection = (sectionId: keyof MonitorData) => {
    setSections(prev =>
      prev.map(section =>
        section.id === sectionId
          ? { ...section, enabled: !section.enabled }
          : section
      )
    );
  };

  // 獲取狀態顏色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
      case 'playing':
      case 'online':
        return 'text-green-600 bg-green-100';
      case 'warning':
      case 'loading':
        return 'text-yellow-600 bg-yellow-100';
      case 'error':
      case 'offline':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // 渲染系統監控
  const renderSystemMonitor = () => (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
        <CpuChipIcon className="h-5 w-5 mr-2 text-blue-500" />
        系統監控
      </h3>
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">記憶體使用</span>
          <div className="flex items-center space-x-2">
            <div className="w-24 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full"
                style={{ width: `${monitorData.system.memory.percentage}%` }}
              />
            </div>
            <span className="text-xs text-gray-500">
              {monitorData.system.memory.percentage}%
            </span>
          </div>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">運行時間</span>
          <span className="text-sm font-medium">{monitorData.system.uptime}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">上次更新</span>
          <span className="text-xs text-gray-500">
            {new Date(monitorData.system.timestamp).toLocaleTimeString()}
          </span>
        </div>
      </div>
    </div>
  );

  // 渲染網路監控
  const renderNetworkMonitor = () => (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
        <GlobeAltIcon className="h-5 w-5 mr-2 text-green-500" />
        網路狀態
      </h3>
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">連線狀態</span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            monitorData.network.online ? 'text-green-600 bg-green-100' : 'text-red-600 bg-red-100'
          }`}>
            {monitorData.network.online ? '線上' : '離線'}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">連線類型</span>
          <span className="text-sm font-medium">{monitorData.network.effectiveType}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">下載速度</span>
          <span className="text-sm font-medium">{monitorData.network.downlink} Mbps</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">延遲</span>
          <span className="text-sm font-medium">{monitorData.network.rtt} ms</span>
        </div>
      </div>
    </div>
  );

  // 渲染播放監控
  const renderPlaybackMonitor = () => (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
        <PlayIcon className="h-5 w-5 mr-2 text-purple-500" />
        播放狀態
      </h3>
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">播放狀態</span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(monitorData.playback.status)}`}>
            {monitorData.playback.status === 'idle' ? '閒置' :
             monitorData.playback.status === 'loading' ? '載入中' :
             monitorData.playback.status === 'playing' ? '播放中' : '錯誤'}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">品質</span>
          <span className="text-sm font-medium">{monitorData.playback.quality}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">解析度</span>
          <span className="text-sm font-medium">{monitorData.playback.resolution}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">緩衝健康度</span>
          <div className="flex items-center space-x-2">
            <div className="w-16 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-purple-500 h-2 rounded-full"
                style={{ width: `${monitorData.playback.bufferHealth}%` }}
              />
            </div>
            <span className="text-xs text-gray-500">
              {monitorData.playback.bufferHealth}%
            </span>
          </div>
        </div>
      </div>
    </div>
  );

  // 渲染效能監控
  const renderPerformanceMonitor = () => (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
        <ChartBarIcon className="h-5 w-5 mr-2 text-orange-500" />
        效能監控
      </h3>
      <div className="space-y-3">
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">載入時間</span>
          <span className="text-sm font-medium">{monitorData.performance.loadTime} ms</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">渲染時間</span>
          <span className="text-sm font-medium">{monitorData.performance.renderTime} ms</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">掉幀次數</span>
          <span className="text-sm font-medium">{monitorData.performance.frameDrops}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">錯誤次數</span>
          <span className="text-sm font-medium">{monitorData.performance.errorCount}</span>
        </div>
      </div>
    </div>
  );

  if (!isVisible) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsVisible(true)}
        leftIcon={<SignalIcon className="h-4 w-4" />}
        className="fixed bottom-4 right-4 z-50 bg-white shadow-lg"
      >
        顯示監控
      </Button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-4xl">
      {/* 控制面板 */}
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-4 mb-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <SignalIcon className="h-5 w-5 mr-2" />
            系統監控面板
          </h2>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setAutoRefresh(!autoRefresh)}
              leftIcon={autoRefresh ? <EyeIcon className="h-4 w-4" /> : <EyeSlashIcon className="h-4 w-4" />}
            >
              {autoRefresh ? '自動刷新' : '手動刷新'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsVisible(false)}
            >
              隱藏
            </Button>
          </div>
        </div>

        {/* 區塊選擇 */}
        <div className="flex flex-wrap gap-2 mb-4">
          {sections.map((section) => {
            const IconComponent = section.icon;
            return (
              <button
                key={section.id}
                onClick={() => toggleSection(section.id)}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  section.enabled
                    ? `text-${section.color}-700 bg-${section.color}-100 border-${section.color}-200`
                    : 'text-gray-500 bg-gray-100 border-gray-200'
                } border`}
              >
                <IconComponent className="h-4 w-4 inline mr-1" />
                {section.title}
              </button>
            );
          })}
        </div>

        {/* 刷新設定 */}
        {autoRefresh && (
          <div className="flex items-center space-x-4 mb-4">
            <label className="text-sm text-gray-600">刷新間隔:</label>
            <select
              value={refreshInterval}
              onChange={(e) => setRefreshInterval(Number(e.target.value))}
              className="text-sm border border-gray-300 rounded px-2 py-1"
            >
              <option value={1000}>1秒</option>
              <option value={2000}>2秒</option>
              <option value={5000}>5秒</option>
              <option value={10000}>10秒</option>
            </select>
            <Button
              variant="outline"
              size="sm"
              onClick={updateMonitorData}
            >
              立即刷新
            </Button>
          </div>
        )}
      </div>

      {/* 監控資料 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {sections.find(s => s.id === 'system')?.enabled && renderSystemMonitor()}
        {sections.find(s => s.id === 'network')?.enabled && renderNetworkMonitor()}
        {sections.find(s => s.id === 'playback')?.enabled && renderPlaybackMonitor()}
        {sections.find(s => s.id === 'performance')?.enabled && renderPerformanceMonitor()}
      </div>
    </div>
  );
};