import React, { useEffect } from 'react';
import {
  CpuChipIcon,
  GlobeAltIcon,
  PlayIcon,
  SignalIcon,
  ChartBarIcon,
  XMarkIcon,
  BellIcon,
  EyeIcon,
  EyeSlashIcon,
  Cog6ToothIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { Button } from './Button';
import { useMonitorStore } from '../../stores/monitorStore';

export const EnhancedMonitorDashboard: React.FC = () => {
  const {
    monitorData,
    settings,
    isVisible,
    alerts,
    updateMonitorData,
    updateSettings,
    toggleVisibility,
    toggleSection,
    dismissAlert,
    clearAlerts,
    getNetworkStatus,
    getMemoryUsage,
    getPerformanceMetrics
  } = useMonitorStore();

  // 自動刷新監控資料
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (settings.autoRefresh && isVisible && settings.enabled) {
      const updateData = () => {
        const networkStatus = getNetworkStatus();
        const memoryUsage = getMemoryUsage();
        const performanceMetrics = getPerformanceMetrics();
        
        updateMonitorData({
          network: networkStatus,
          memory: memoryUsage,
          performance: performanceMetrics,
          uptime: Math.round((Date.now() - performance.timeOrigin) / 1000)
        });
      };

      updateData(); // 立即更新一次
      interval = setInterval(updateData, settings.refreshInterval);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [settings.autoRefresh, isVisible, settings.enabled, settings.refreshInterval]);

  // 獲取位置樣式
  const getPositionStyles = () => {
    switch (settings.position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      default:
        return 'bottom-4 right-4';
    }
  };

  // 渲染警告通知
  const renderAlerts = () => {
    const activeAlerts = alerts.filter(alert => !alert.dismissed);
    
    if (activeAlerts.length === 0) return null;

    return (
      <div className="mb-4 space-y-2">
        {activeAlerts.slice(0, 3).map((alert) => (
          <div
            key={alert.id}
            className={`flex items-center justify-between p-3 rounded-lg border ${
              alert.type === 'error' ? 'bg-red-50 border-red-200 text-red-800' :
              alert.type === 'warning' ? 'bg-yellow-50 border-yellow-200 text-yellow-800' :
              'bg-blue-50 border-blue-200 text-blue-800'
            }`}
          >
            <div className="flex items-center">
              <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
              <span className="text-sm">{alert.message}</span>
            </div>
            <button
              onClick={() => dismissAlert(alert.id)}
              className="text-current hover:opacity-75"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          </div>
        ))}
      </div>
    );
  };

  // 渲染系統監控
  const renderSystemMonitor = () => (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
        <CpuChipIcon className="h-5 w-5 mr-2 text-blue-500" />
        系統監控
      </h3>
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">記憶體使用</span>
          <div className="flex items-center space-x-2">
            <div className="w-24 bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${
                  monitorData.memory.percentage > 80 ? 'bg-red-500' :
                  monitorData.memory.percentage > 60 ? 'bg-yellow-500' : 'bg-blue-500'
                }`}
                style={{ width: `${monitorData.memory.percentage}%` }}
              />
            </div>
            <span className="text-xs text-gray-500">
              {monitorData.memory.percentage}%
            </span>
          </div>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">已用記憶體</span>
          <span className="text-sm font-medium">{monitorData.memory.used} MB</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">運行時間</span>
          <span className="text-sm font-medium">{monitorData.uptime}s</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">最後更新</span>
          <span className="text-xs text-gray-500">
            {new Date(monitorData.timestamp).toLocaleTimeString()}
          </span>
        </div>
      </div>
    </div>
  );

  // 渲染網路監控
  const renderNetworkMonitor = () => (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
        <GlobeAltIcon className="h-5 w-5 mr-2 text-green-500" />
        網路狀態
      </h3>
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">連線狀態</span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            monitorData.network.online ? 'text-green-600 bg-green-100' : 'text-red-600 bg-red-100'
          }`}>
            {monitorData.network.online ? '線上' : '離線'}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">連線類型</span>
          <span className="text-sm font-medium">{monitorData.network.effectiveType}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">下載速度</span>
          <span className="text-sm font-medium">{monitorData.network.downlink} Mbps</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">延遲</span>
          <span className={`text-sm font-medium ${
            monitorData.network.rtt > 500 ? 'text-red-600' :
            monitorData.network.rtt > 200 ? 'text-yellow-600' : 'text-green-600'
          }`}>
            {monitorData.network.rtt} ms
          </span>
        </div>
      </div>
    </div>
  );

  // 渲染播放監控
  const renderPlaybackMonitor = () => (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
        <PlayIcon className="h-5 w-5 mr-2 text-purple-500" />
        播放狀態
      </h3>
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">播放狀態</span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            monitorData.playback.status === 'playing' ? 'text-green-600 bg-green-100' :
            monitorData.playback.status === 'loading' || monitorData.playback.status === 'buffering' ? 'text-yellow-600 bg-yellow-100' :
            monitorData.playback.status === 'error' ? 'text-red-600 bg-red-100' :
            'text-gray-600 bg-gray-100'
          }`}>
            {monitorData.playback.status === 'idle' ? '閒置' :
             monitorData.playback.status === 'loading' ? '載入中' :
             monitorData.playback.status === 'playing' ? '播放中' :
             monitorData.playback.status === 'buffering' ? '緩衝中' : '錯誤'}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">品質</span>
          <span className="text-sm font-medium">{monitorData.playback.quality}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">解析度</span>
          <span className="text-sm font-medium">{monitorData.playback.resolution}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">位元率</span>
          <span className="text-sm font-medium">{monitorData.playback.bitrate} kbps</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">緩衝健康度</span>
          <div className="flex items-center space-x-2">
            <div className="w-16 bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${
                  monitorData.playback.bufferHealth > 80 ? 'bg-green-500' :
                  monitorData.playback.bufferHealth > 50 ? 'bg-yellow-500' : 'bg-red-500'
                }`}
                style={{ width: `${monitorData.playback.bufferHealth}%` }}
              />
            </div>
            <span className="text-xs text-gray-500">
              {monitorData.playback.bufferHealth}%
            </span>
          </div>
        </div>
        {monitorData.playback.errors.length > 0 && (
          <div className="mt-2 p-2 bg-red-50 rounded border border-red-200">
            <span className="text-xs text-red-600">
              最近錯誤: {monitorData.playback.errors[monitorData.playback.errors.length - 1]}
            </span>
          </div>
        )}
      </div>
    </div>
  );

  // 渲染效能監控
  const renderPerformanceMonitor = () => (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
        <ChartBarIcon className="h-5 w-5 mr-2 text-orange-500" />
        效能監控
      </h3>
      <div className="space-y-3">
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">載入時間</span>
          <span className={`text-sm font-medium ${
            monitorData.performance.loadTime > 3000 ? 'text-red-600' :
            monitorData.performance.loadTime > 1000 ? 'text-yellow-600' : 'text-green-600'
          }`}>
            {monitorData.performance.loadTime} ms
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">渲染時間</span>
          <span className={`text-sm font-medium ${
            monitorData.performance.renderTime > 1000 ? 'text-red-600' :
            monitorData.performance.renderTime > 500 ? 'text-yellow-600' : 'text-green-600'
          }`}>
            {monitorData.performance.renderTime} ms
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">錯誤次數</span>
          <span className={`text-sm font-medium ${
            monitorData.performance.errorCount > 5 ? 'text-red-600' :
            monitorData.performance.errorCount > 2 ? 'text-yellow-600' : 'text-green-600'
          }`}>
            {monitorData.performance.errorCount}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">掉幀次數</span>
          <span className="text-sm font-medium">{monitorData.performance.frameDrops}</span>
        </div>
      </div>
    </div>
  );

  if (!settings.enabled) {
    return null;
  }

  if (!isVisible) {
    return (
      <div className={`fixed ${getPositionStyles()} z-50`}>
        {alerts.filter(a => !a.dismissed).length > 0 && (
          <div className="mb-2">
            <Button
              variant="outline"
              size="sm"
              onClick={toggleVisibility}
              leftIcon={<BellIcon className="h-4 w-4 text-red-500" />}
              className="bg-white shadow-lg border-red-200 text-red-600"
            >
              {alerts.filter(a => !a.dismissed).length} 個警告
            </Button>
          </div>
        )}
        <Button
          variant="outline"
          size="sm"
          onClick={toggleVisibility}
          leftIcon={<SignalIcon className="h-4 w-4" />}
          className="bg-white shadow-lg"
        >
          顯示監控
        </Button>
      </div>
    );
  }

  return (
    <div className={`fixed ${getPositionStyles()} z-50 ${settings.compact ? 'max-w-md' : 'max-w-4xl'}`}>
      {/* 警告通知 */}
      {renderAlerts()}

      {/* 控制面板 */}
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-4 mb-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <SignalIcon className="h-5 w-5 mr-2" />
            系統監控面板
            {alerts.filter(a => !a.dismissed).length > 0 && (
              <span className="ml-2 px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full">
                {alerts.filter(a => !a.dismissed).length}
              </span>
            )}
          </h2>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => updateSettings({ autoRefresh: !settings.autoRefresh })}
              leftIcon={settings.autoRefresh ? <EyeIcon className="h-4 w-4" /> : <EyeSlashIcon className="h-4 w-4" />}
            >
              {settings.autoRefresh ? '自動刷新' : '手動刷新'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => updateSettings({ compact: !settings.compact })}
              leftIcon={<Cog6ToothIcon className="h-4 w-4" />}
            >
              {settings.compact ? '展開' : '精簡'}
            </Button>
            {alerts.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearAlerts}
                className="text-red-600"
              >
                清除警告
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={toggleVisibility}
            >
              隱藏
            </Button>
          </div>
        </div>

        {/* 區塊選擇 */}
        {!settings.compact && (
          <div className="flex flex-wrap gap-2 mb-4">
            <button
              onClick={() => toggleSection('system')}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors border ${
                settings.visibleSections.system
                  ? 'text-blue-700 bg-blue-100 border-blue-200'
                  : 'text-gray-500 bg-gray-100 border-gray-200'
              }`}
            >
              <CpuChipIcon className="h-4 w-4 inline mr-1" />
              系統監控
            </button>
            <button
              onClick={() => toggleSection('network')}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors border ${
                settings.visibleSections.network
                  ? 'text-green-700 bg-green-100 border-green-200'
                  : 'text-gray-500 bg-gray-100 border-gray-200'
              }`}
            >
              <GlobeAltIcon className="h-4 w-4 inline mr-1" />
              網路狀態
            </button>
            <button
              onClick={() => toggleSection('playback')}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors border ${
                settings.visibleSections.playback
                  ? 'text-purple-700 bg-purple-100 border-purple-200'
                  : 'text-gray-500 bg-gray-100 border-gray-200'
              }`}
            >
              <PlayIcon className="h-4 w-4 inline mr-1" />
              播放狀態
            </button>
            <button
              onClick={() => toggleSection('performance')}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors border ${
                settings.visibleSections.performance
                  ? 'text-orange-700 bg-orange-100 border-orange-200'
                  : 'text-gray-500 bg-gray-100 border-gray-200'
              }`}
            >
              <ChartBarIcon className="h-4 w-4 inline mr-1" />
              效能監控
            </button>
          </div>
        )}

        {/* 刷新設定 */}
        {settings.autoRefresh && !settings.compact && (
          <div className="flex items-center space-x-4 mb-4">
            <label className="text-sm text-gray-600">刷新間隔:</label>
            <select
              value={settings.refreshInterval}
              onChange={(e) => updateSettings({ refreshInterval: Number(e.target.value) })}
              className="text-sm border border-gray-300 rounded px-2 py-1"
            >
              <option value={1000}>1秒</option>
              <option value={2000}>2秒</option>
              <option value={5000}>5秒</option>
              <option value={10000}>10秒</option>
            </select>
          </div>
        )}
      </div>

      {/* 監控資料 */}
      <div className={`grid ${settings.compact ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2'} gap-4`}>
        {settings.visibleSections.system && renderSystemMonitor()}
        {settings.visibleSections.network && renderNetworkMonitor()}
        {settings.visibleSections.playback && renderPlaybackMonitor()}
        {settings.visibleSections.performance && renderPerformanceMonitor()}
      </div>
    </div>
  );
};