import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useAppStore } from './stores/appStore';
import { initializeMonitorStore } from './stores/monitorStore';
import { Home } from './pages/Home/Home';
import { ChannelListPage } from './pages/ChannelList/ChannelListPage';
import { PlayerPage } from './pages/Player/PlayerPage';
import { SettingsPage } from './pages/Settings/SettingsPage';
import { M3UTestPage } from './pages/M3UTest';
import { DiagnosticPage } from './pages/Diagnostic/DiagnosticPage';
import { DebugPage } from './pages/Debug/DebugPage';
import { Navigation } from './components/Navigation/Navigation';
import { ErrorBoundary } from './components/ErrorBoundary/ErrorBoundary';
import { EnhancedMonitorDashboard } from './components/ui/EnhancedMonitorDashboard';

function App() {
  const { initializeApp } = useAppStore();

  useEffect(() => {
    // 初始化應用程式
    initializeApp();
    
    // 初始化監控系統
    initializeMonitorStore();
  }, [initializeApp]);

  return (
    <ErrorBoundary>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Navigation />
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/channels" element={<ChannelListPage />} />
            <Route path="/player" element={<PlayerPage />} />
            <Route path="/settings" element={<SettingsPage />} />
            <Route path="/test" element={<M3UTestPage />} />
            <Route path="/diagnostic" element={<DiagnosticPage />} />
            <Route path="/debug" element={<DebugPage />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
          
          {/* 系統監控面板 */}
          <EnhancedMonitorDashboard />
        </div>
      </Router>
    </ErrorBoundary>
  );
}

export default App;
