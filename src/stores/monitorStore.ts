import { create } from 'zustand';

export interface SystemMonitorData {
  cpu: {
    usage: number;
    temperature: number;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  network: {
    online: boolean;
    connectionType: string;
    effectiveType: string;
    downlink: number;
    rtt: number;
    saveData: boolean;
  };
  playback: {
    currentUrl: string | null;
    status: 'idle' | 'loading' | 'playing' | 'error' | 'buffering';
    bufferHealth: number;
    quality: string;
    fps: number;
    resolution: string;
    bitrate: number;
    errors: string[];
  };
  performance: {
    loadTime: number;
    renderTime: number;
    frameDrops: number;
    errorCount: number;
    memoryLeaks: number;
  };
  timestamp: string;
  uptime: number;
}

export interface MonitorSettings {
  enabled: boolean;
  autoRefresh: boolean;
  refreshInterval: number;
  visibleSections: {
    system: boolean;
    network: boolean;
    playback: boolean;
    performance: boolean;
  };
  alertThresholds: {
    memoryUsage: number;
    networkLatency: number;
    errorCount: number;
  };
  position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  compact: boolean;
}

interface MonitorStore {
  // 狀態
  monitorData: SystemMonitorData;
  settings: MonitorSettings;
  isVisible: boolean;
  alerts: Array<{
    id: string;
    type: 'warning' | 'error' | 'info';
    message: string;
    timestamp: string;
    dismissed: boolean;
  }>;

  // 動作
  updateMonitorData: (data: Partial<SystemMonitorData>) => void;
  updateSettings: (settings: Partial<MonitorSettings>) => void;
  toggleVisibility: () => void;
  toggleSection: (section: keyof MonitorSettings['visibleSections']) => void;
  addAlert: (type: 'warning' | 'error' | 'info', message: string) => void;
  dismissAlert: (alertId: string) => void;
  clearAlerts: () => void;
  updatePlaybackStatus: (status: Partial<SystemMonitorData['playback']>) => void;
  recordError: (error: string) => void;
  getNetworkStatus: () => SystemMonitorData['network'];
  getMemoryUsage: () => SystemMonitorData['memory'];
  getPerformanceMetrics: () => SystemMonitorData['performance'];
}

const defaultSettings: MonitorSettings = {
  enabled: true,
  autoRefresh: true,
  refreshInterval: 2000,
  visibleSections: {
    system: true,
    network: true,
    playback: true,
    performance: false
  },
  alertThresholds: {
    memoryUsage: 80,
    networkLatency: 1000,
    errorCount: 5
  },
  position: 'bottom-right',
  compact: false
};

const defaultMonitorData: SystemMonitorData = {
  cpu: {
    usage: 0,
    temperature: 0
  },
  memory: {
    used: 0,
    total: 0,
    percentage: 0
  },
  network: {
    online: navigator.onLine,
    connectionType: 'unknown',
    effectiveType: 'unknown',
    downlink: 0,
    rtt: 0,
    saveData: false
  },
  playback: {
    currentUrl: null,
    status: 'idle',
    bufferHealth: 0,
    quality: 'auto',
    fps: 0,
    resolution: 'unknown',
    bitrate: 0,
    errors: []
  },
  performance: {
    loadTime: 0,
    renderTime: 0,
    frameDrops: 0,
    errorCount: 0,
    memoryLeaks: 0
  },
  timestamp: new Date().toISOString(),
  uptime: 0
};

export const useMonitorStore = create<MonitorStore>((set, get) => ({
  // 初始狀態
  monitorData: defaultMonitorData,
  settings: defaultSettings,
  isVisible: false,
  alerts: [],

  // 動作
  updateMonitorData: (data) => {
    const currentData = get().monitorData;
    const newData = { ...currentData, ...data, timestamp: new Date().toISOString() };
    
    set({ monitorData: newData });

    // 檢查警告閾值
    const { settings, addAlert } = get();
    
    if (newData.memory.percentage > settings.alertThresholds.memoryUsage) {
      addAlert('warning', `記憶體使用率過高: ${newData.memory.percentage}%`);
    }
    
    if (newData.network.rtt > settings.alertThresholds.networkLatency) {
      addAlert('warning', `網路延遲過高: ${newData.network.rtt}ms`);
    }
    
    if (newData.performance.errorCount > settings.alertThresholds.errorCount) {
      addAlert('error', `錯誤次數過多: ${newData.performance.errorCount}`);
    }
  },

  updateSettings: (newSettings) => {
    const currentSettings = get().settings;
    const updatedSettings = { ...currentSettings, ...newSettings };
    
    set({ settings: updatedSettings });
    
    // 儲存到 localStorage
    localStorage.setItem('tvbox-monitor-settings', JSON.stringify(updatedSettings));
  },

  toggleVisibility: () => {
    set((state) => ({ isVisible: !state.isVisible }));
  },

  toggleSection: (section) => {
    const currentSettings = get().settings;
    const updatedSettings = {
      ...currentSettings,
      visibleSections: {
        ...currentSettings.visibleSections,
        [section]: !currentSettings.visibleSections[section]
      }
    };
    
    get().updateSettings(updatedSettings);
  },

  addAlert: (type, message) => {
    const alert = {
      id: Date.now().toString(),
      type,
      message,
      timestamp: new Date().toISOString(),
      dismissed: false
    };
    
    set((state) => ({
      alerts: [...state.alerts.filter(a => !a.dismissed), alert].slice(-10) // 最多保留10個警告
    }));
  },

  dismissAlert: (alertId) => {
    set((state) => ({
      alerts: state.alerts.map(alert =>
        alert.id === alertId ? { ...alert, dismissed: true } : alert
      )
    }));
  },

  clearAlerts: () => {
    set({ alerts: [] });
  },

  updatePlaybackStatus: (status) => {
    const currentData = get().monitorData;
    const updatedPlayback = { ...currentData.playback, ...status };
    
    get().updateMonitorData({ playback: updatedPlayback });
  },

  recordError: (error) => {
    const currentData = get().monitorData;
    const updatedPlayback = {
      ...currentData.playback,
      errors: [...currentData.playback.errors, error].slice(-5) // 最多保留5個錯誤
    };
    const updatedPerformance = {
      ...currentData.performance,
      errorCount: currentData.performance.errorCount + 1
    };
    
    get().updateMonitorData({
      playback: updatedPlayback,
      performance: updatedPerformance
    });
    
    get().addAlert('error', error);
  },

  getNetworkStatus: () => {
    const connection = (navigator as any).connection || 
                     (navigator as any).mozConnection || 
                     (navigator as any).webkitConnection;
    
    if (connection) {
      return {
        online: navigator.onLine,
        connectionType: connection.type || 'unknown',
        effectiveType: connection.effectiveType || 'unknown',
        downlink: connection.downlink || 0,
        rtt: connection.rtt || 0,
        saveData: connection.saveData || false
      };
    }

    return {
      online: navigator.onLine,
      connectionType: 'unknown',
      effectiveType: 'unknown',
      downlink: 0,
      rtt: 0,
      saveData: false
    };
  },

  getMemoryUsage: () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
        percentage: Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100)
      };
    }
    return { used: 0, total: 0, percentage: 0 };
  },

  getPerformanceMetrics: () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    return {
      loadTime: navigation ? Math.round(navigation.loadEventEnd - navigation.fetchStart) : 0,
      renderTime: navigation ? Math.round(navigation.domContentLoadedEventEnd - navigation.fetchStart) : 0,
      frameDrops: 0, // 需要通過其他方式追蹤
      errorCount: get().monitorData.performance.errorCount,
      memoryLeaks: 0 // 需要通過其他方式追蹤
    };
  }
}));

// 初始化設定
const initializeMonitorStore = () => {
  const savedSettings = localStorage.getItem('tvbox-monitor-settings');
  
  if (savedSettings) {
    try {
      const parsedSettings = JSON.parse(savedSettings);
      useMonitorStore.getState().updateSettings(parsedSettings);
    } catch (error) {
      console.warn('Failed to load monitor settings:', error);
    }
  }
  
  // 監聽網路狀態變化
  window.addEventListener('online', () => {
    useMonitorStore.getState().updateMonitorData({
      network: useMonitorStore.getState().getNetworkStatus()
    });
  });
  
  window.addEventListener('offline', () => {
    useMonitorStore.getState().updateMonitorData({
      network: useMonitorStore.getState().getNetworkStatus()
    });
  });
};

// 匯出初始化函數
export { initializeMonitorStore }; 