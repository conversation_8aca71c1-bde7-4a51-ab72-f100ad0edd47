# TVBOX 監控系統實現說明

## 功能概述

我已經為 TVBOX-Abuji20022 專案實現了一個完整的可選擇顯示監控資訊系統，讓用戶可以實時監控應用的運行狀況。

## 已實現的功能

### 1. 核心監控組件

#### `EnhancedMonitorDashboard` (`src/components/ui/EnhancedMonitorDashboard.tsx`)
- 可浮動顯示的監控面板
- 支援多個位置顯示（右下角、左下角、右上角、左上角）
- 支援精簡模式和完整模式
- 可選擇顯示不同的監控模組

#### `MonitorStore` (`src/stores/monitorStore.ts`)
- 使用 Zustand 進行狀態管理
- 自動儲存設定到 localStorage
- 支援警告閾值設定和自動警告

### 2. 監控模組

#### 系統監控
- **記憶體使用率**：顯示 JavaScript 堆記憶體使用情況
- **運行時間**：應用啟動以來的運行時間
- **最後更新時間**：監控數據的最後更新時間

#### 網路狀態監控
- **連線狀態**：線上/離線狀態
- **連線類型**：網路連線的有效類型（4g、wifi 等）
- **下載速度**：網路下載速度（如果支援）
- **延遲**：網路往返時間

#### 播放狀態監控
- **播放狀態**：閒置、載入中、播放中、緩衝中、錯誤
- **品質設定**：當前播放品質
- **解析度**：視頻解析度
- **位元率**：視頻位元率
- **緩衝健康度**：緩衝區健康狀況
- **錯誤記錄**：最近的播放錯誤

#### 效能監控
- **載入時間**：頁面載入時間
- **渲染時間**：DOM 渲染時間
- **錯誤次數**：累計錯誤次數
- **掉幀次數**：視頻播放掉幀統計

### 3. 設定選項

#### 基本設定
- **啟用/停用監控**：完全控制監控系統開關
- **自動刷新**：可選擇是否自動更新監控數據
- **刷新間隔**：1秒、2秒、5秒、10秒選項
- **顯示位置**：四個角落位置選擇
- **精簡模式**：較小的監控面板

#### 模組選擇
用戶可以個別選擇顯示的監控模組：
- ✅ 系統監控
- ✅ 網路狀態  
- ✅ 播放狀態
- ✅ 效能監控

#### 警告閾值設定
- **記憶體使用率警告**：可設定 50-95% 的警告閾值
- **網路延遲警告**：可設定 100-2000ms 的警告閾值
- **錯誤次數警告**：可設定 1-20 次的警告閾值

### 4. 警告系統

#### 實時警告
- 當監控數據超過設定閾值時自動彈出警告
- 支援三種警告類型：錯誤、警告、資訊
- 警告可以個別關閉
- 最多同時顯示 3 個警告，舊的會自動清除

#### 警告指示器
- 當有未讀警告時，監控按鈕會顯示紅色提示
- 標題欄會顯示警告數量

### 5. 與播放器整合

#### 自動狀態同步
已將監控系統與播放器狀態管理整合：
- 播放/暫停時自動更新監控狀態
- 頻道切換時更新當前 URL
- 播放錯誤時自動記錄到監控系統
- 載入狀態變化時同步更新

## 使用方式

### 1. 啟用監控
1. 進入設定頁面（`/settings`）
2. 找到「系統監控設定」區塊
3. 開啟「啟用系統監控」開關

### 2. 調整設定
在設定頁面中可以：
- 選擇要顯示的監控模組
- 設定監控面板的位置和樣式
- 調整警告閾值
- 配置自動刷新設定

### 3. 查看監控資訊
- 點擊右下角（或其他設定位置）的「顯示監控」按鈕
- 監控面板會顯示所有啟用的監控模組
- 可以隨時隱藏或展開監控面板

### 4. 響應警告
- 當出現警告時，會在監控面板頂部顯示
- 點擊警告右側的 ✕ 可以關閉個別警告
- 點擊「清除警告」可以清除所有警告

## 技術特點

### 1. 模組化設計
- 每個監控模組都是獨立的
- 可以輕鬆添加新的監控項目
- 支援個別開關每個模組

### 2. 效能優化
- 使用 `useEffect` 進行資源清理
- 只在顯示時才進行資料更新
- 支援自訂刷新間隔以平衡即時性和效能

### 3. 響應式設計
- 支援不同螢幕尺寸
- 精簡模式適合小螢幕裝置
- 使用 Tailwind CSS 進行樣式設計

### 4. 資料持久化
- 監控設定自動儲存到 localStorage
- 重新載入頁面後保持設定
- 警告記錄可以保留一定數量

### 5. 錯誤處理
- 完善的錯誤邊界處理
- 自動錯誤記錄和分類
- 友善的錯誤提示

## 擴展性

這個監控系統設計具有良好的擴展性：

1. **新增監控項目**：在 `MonitorStore` 中添加新的數據字段
2. **新增警告類型**：在警告系統中添加新的閾值檢查
3. **新增顯示樣式**：在 `EnhancedMonitorDashboard` 中添加新的渲染函數
4. **整合其他組件**：任何組件都可以通過 `useMonitorStore` 更新監控狀態

## 結論

這個監控系統為 TVBOX 應用提供了全面的運行狀態監控能力，用戶可以：
- 即時了解應用運行狀況
- 及時發現和解決問題
- 個性化配置監控選項
- 獲得智能的警告提醒

所有功能都已整合到現有的應用架構中，並且與原有的設定系統完美結合。 